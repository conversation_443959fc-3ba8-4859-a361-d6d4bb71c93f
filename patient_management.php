<?php
/**
 * Patient Management Page
 * Hospital Reservation System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/Patient.php';

$auth = new Auth();
requireLogin();
requireRole(['admin', 'doctor']);

$patient = new Patient();
$current_page = 'patients';
$page_title = 'Patient Management';
$page_subtitle = 'Manage patient records and information';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'create':
                $userData = [
                    'email' => $_POST['email'],
                    'full_name' => $_POST['full_name'],
                    'phone' => $_POST['phone'],
                    'password_hash' => password_hash($_POST['password'], PASSWORD_DEFAULT)
                ];

                $patientData = [
                    'date_of_birth' => $_POST['date_of_birth'],
                    'gender' => $_POST['gender'],
                    'address' => $_POST['address'],
                    'emergency_contact_name' => $_POST['emergency_contact_name'],
                    'emergency_contact_phone' => $_POST['emergency_contact_phone']
                ];

                $patientId = $patient->createPatient($userData, $patientData);
                echo json_encode(['success' => true, 'message' => 'Patient created successfully', 'patient_id' => $patientId]);
                break;

            case 'update':
                $userData = [
                    'full_name' => $_POST['full_name'],
                    'phone' => $_POST['phone']
                ];

                $patientData = [
                    'date_of_birth' => $_POST['date_of_birth'],
                    'gender' => $_POST['gender'],
                    'address' => $_POST['address'],
                    'emergency_contact_name' => $_POST['emergency_contact_name'],
                    'emergency_contact_phone' => $_POST['emergency_contact_phone']
                ];

                $patient->updatePatient($_POST['patient_id'], $userData, $patientData);
                echo json_encode(['success' => true, 'message' => 'Patient updated successfully']);
                break;

            case 'delete':
                $patient->deletePatient($_POST['patient_id']);
                echo json_encode(['success' => true, 'message' => 'Patient deleted successfully']);
                break;

            case 'get':
                $patientData = $patient->getPatientById($_POST['patient_id']);
                if ($patientData) {
                    echo json_encode(['success' => true, 'patient' => $patientData]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Patient not found']);
                }
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Get patients
$patients = $patient->getAllPatients();

ob_start();
?>

<!-- Patients Table -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 class="card-title">Patients (<?php echo count($patients); ?>)</h2>
            <button type="button" class="btn btn-primary" onclick="openAddPatientModal()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
                Add Patient
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($patients)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">No patients found.</p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>MRN</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Date of Birth</th>
                            <th>Gender</th>
                            <th>Registered</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $patientRecord): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($patientRecord['medical_record_number']); ?></td>
                                <td><?php echo htmlspecialchars($patientRecord['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($patientRecord['email']); ?></td>
                                <td><?php echo htmlspecialchars($patientRecord['phone'] ?? '-'); ?></td>
                                <td><?php echo $patientRecord['date_of_birth'] ? date('M j, Y', strtotime($patientRecord['date_of_birth'])) : '-'; ?></td>
                                <td><?php echo ucfirst($patientRecord['gender'] ?? '-'); ?></td>
                                <td><?php echo date('M j, Y', strtotime($patientRecord['created_at'])); ?></td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem;">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="viewPatient('<?php echo $patientRecord['id']; ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                                <circle cx="12" cy="12" r="3"/>
                                            </svg>
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="editPatient('<?php echo $patientRecord['id']; ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                            </svg>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deletePatient('<?php echo $patientRecord['id']; ?>', '<?php echo htmlspecialchars($patientRecord['full_name']); ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"/>
                                                <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Patient Modal -->
<div id="patientModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Add Patient</h3>
            <span class="close" onclick="closePatientModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="patientForm">
                <input type="hidden" id="patientId" name="patient_id">
                <input type="hidden" id="formAction" name="action" value="create">

                <div class="form-row">
                    <div class="form-group">
                        <label for="fullName">Full Name *</label>
                        <input type="text" id="fullName" name="full_name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-group" id="passwordGroup">
                        <label for="password">Password *</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth *</label>
                        <input type="date" id="dateOfBirth" name="date_of_birth" required>
                    </div>
                    <div class="form-group">
                        <label for="gender">Gender *</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Address *</label>
                    <textarea id="address" name="address" rows="3" required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="emergencyContactName">Emergency Contact Name *</label>
                        <input type="text" id="emergencyContactName" name="emergency_contact_name" required>
                    </div>
                    <div class="form-group">
                        <label for="emergencyContactPhone">Emergency Contact Phone *</label>
                        <input type="tel" id="emergencyContactPhone" name="emergency_contact_phone" required>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closePatientModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="savePatient()">Save Patient</button>
        </div>
    </div>
</div>

<!-- View Patient Modal -->
<div id="viewPatientModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Patient Details</h3>
            <span class="close" onclick="closeViewPatientModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="patientDetails"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeViewPatientModal()">Close</button>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
}

.close {
    color: #6b7280;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    flex: 1;
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.patient-detail-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 4px;
}

.patient-detail-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.patient-detail-value {
    color: #6b7280;
}
</style>

<script>
// Patient Management Functions
function openAddPatientModal() {
    document.getElementById('modalTitle').textContent = 'Add Patient';
    document.getElementById('formAction').value = 'create';
    document.getElementById('patientId').value = '';
    document.getElementById('patientForm').reset();
    document.getElementById('passwordGroup').style.display = 'block';
    document.getElementById('email').disabled = false;
    document.getElementById('patientModal').style.display = 'block';
}

function editPatient(patientId) {
    // Get patient data
    fetch('patient_management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get&patient_id=' + patientId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const patient = data.patient;
            document.getElementById('modalTitle').textContent = 'Edit Patient';
            document.getElementById('formAction').value = 'update';
            document.getElementById('patientId').value = patient.id;
            document.getElementById('fullName').value = patient.full_name;
            document.getElementById('email').value = patient.email;
            document.getElementById('email').disabled = true;
            document.getElementById('phone').value = patient.phone || '';
            document.getElementById('dateOfBirth').value = patient.date_of_birth;
            document.getElementById('gender').value = patient.gender;
            document.getElementById('address').value = patient.address;
            document.getElementById('emergencyContactName').value = patient.emergency_contact_name;
            document.getElementById('emergencyContactPhone').value = patient.emergency_contact_phone;
            document.getElementById('passwordGroup').style.display = 'none';
            document.getElementById('patientModal').style.display = 'block';
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error loading patient data', 'error');
    });
}

function viewPatient(patientId) {
    // Get patient data
    fetch('patient_management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get&patient_id=' + patientId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const patient = data.patient;
            const detailsHtml = `
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Medical Record Number</div>
                    <div class="patient-detail-value">${patient.medical_record_number}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Full Name</div>
                    <div class="patient-detail-value">${patient.full_name}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Email</div>
                    <div class="patient-detail-value">${patient.email}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Phone</div>
                    <div class="patient-detail-value">${patient.phone || 'Not provided'}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Date of Birth</div>
                    <div class="patient-detail-value">${new Date(patient.date_of_birth).toLocaleDateString()}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Gender</div>
                    <div class="patient-detail-value">${patient.gender.charAt(0).toUpperCase() + patient.gender.slice(1)}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Address</div>
                    <div class="patient-detail-value">${patient.address}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Emergency Contact</div>
                    <div class="patient-detail-value">${patient.emergency_contact_name} - ${patient.emergency_contact_phone}</div>
                </div>
                <div class="patient-detail-item">
                    <div class="patient-detail-label">Registered</div>
                    <div class="patient-detail-value">${new Date(patient.created_at).toLocaleDateString()}</div>
                </div>
            `;
            document.getElementById('patientDetails').innerHTML = detailsHtml;
            document.getElementById('viewPatientModal').style.display = 'block';
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error loading patient data', 'error');
    });
}

function deletePatient(patientId, patientName) {
    if (confirm(`Are you sure you want to delete patient "${patientName}"? This action cannot be undone.`)) {
        fetch('patient_management.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&patient_id=' + patientId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('Error deleting patient', 'error');
        });
    }
}

function savePatient() {
    // Validate form before submission
    if (!validatePatientForm()) {
        return;
    }

    const form = document.getElementById('patientForm');
    const formData = new FormData(form);

    // Convert FormData to URLSearchParams for proper encoding
    const params = new URLSearchParams();
    for (const [key, value] of formData.entries()) {
        params.append(key, value);
    }

    fetch('patient_management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            closePatientModal();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error saving patient', 'error');
    });
}

function closePatientModal() {
    document.getElementById('patientModal').style.display = 'none';
    document.getElementById('patientForm').reset();
}

function closeViewPatientModal() {
    document.getElementById('viewPatientModal').style.display = 'none';
}

function validatePatientForm() {
    const errors = [];

    // Full name validation
    const fullName = document.getElementById('fullName').value.trim();
    if (fullName.length < 2) {
        errors.push('Full name must be at least 2 characters long');
        highlightField('fullName', true);
    } else {
        highlightField('fullName', false);
    }

    // Email validation (only for new patients)
    const isUpdate = document.getElementById('formAction').value === 'update';
    if (!isUpdate) {
        const email = document.getElementById('email').value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            errors.push('Please enter a valid email address');
            highlightField('email', true);
        } else {
            highlightField('email', false);
        }

        // Password validation
        const password = document.getElementById('password').value;
        if (password.length < 6) {
            errors.push('Password must be at least 6 characters long');
            highlightField('password', true);
        } else {
            highlightField('password', false);
        }
    }

    // Phone validation
    const phone = document.getElementById('phone').value.trim();
    if (phone && !/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(phone)) {
        errors.push('Please enter a valid phone number');
        highlightField('phone', true);
    } else {
        highlightField('phone', false);
    }

    // Date of birth validation
    const dateOfBirth = document.getElementById('dateOfBirth').value;
    if (!dateOfBirth) {
        errors.push('Date of birth is required');
        highlightField('dateOfBirth', true);
    } else {
        const dob = new Date(dateOfBirth);
        const today = new Date();
        const age = today.getFullYear() - dob.getFullYear();

        if (dob > today) {
            errors.push('Date of birth cannot be in the future');
            highlightField('dateOfBirth', true);
        } else if (age > 150) {
            errors.push('Invalid date of birth - age cannot exceed 150 years');
            highlightField('dateOfBirth', true);
        } else {
            highlightField('dateOfBirth', false);
        }
    }

    // Gender validation
    const gender = document.getElementById('gender').value;
    if (!gender) {
        errors.push('Please select a gender');
        highlightField('gender', true);
    } else {
        highlightField('gender', false);
    }

    // Address validation
    const address = document.getElementById('address').value.trim();
    if (address.length < 10) {
        errors.push('Address must be at least 10 characters long');
        highlightField('address', true);
    } else {
        highlightField('address', false);
    }

    // Emergency contact validation
    const emergencyContactName = document.getElementById('emergencyContactName').value.trim();
    if (emergencyContactName.length < 2) {
        errors.push('Emergency contact name must be at least 2 characters long');
        highlightField('emergencyContactName', true);
    } else {
        highlightField('emergencyContactName', false);
    }

    const emergencyContactPhone = document.getElementById('emergencyContactPhone').value.trim();
    if (!/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(emergencyContactPhone)) {
        errors.push('Please enter a valid emergency contact phone number');
        highlightField('emergencyContactPhone', true);
    } else {
        highlightField('emergencyContactPhone', false);
    }

    if (errors.length > 0) {
        showAlert('Please fix the following errors:\n• ' + errors.join('\n• '), 'error');
        return false;
    }

    return true;
}

function highlightField(fieldId, hasError) {
    const field = document.getElementById(fieldId);
    if (hasError) {
        field.style.borderColor = '#ef4444';
        field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
    } else {
        field.style.borderColor = '#d1d5db';
        field.style.boxShadow = 'none';
    }
}

// Alert function
function showAlert(message, type) {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        ${type === 'success' ? 'background-color: #10b981;' : 'background-color: #ef4444;'}
    `;
    alert.textContent = message;

    document.body.appendChild(alert);

    // Remove alert after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Loading indicator functions
function showLoading(buttonId) {
    const button = document.getElementById(buttonId) || document.querySelector(`button[onclick*="${buttonId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<span style="display: inline-block; width: 16px; height: 16px; border: 2px solid #ffffff; border-radius: 50%; border-top-color: transparent; animation: spin 1s linear infinite;"></span> Loading...';
    }
}

function hideLoading(buttonId, originalText) {
    const button = document.getElementById(buttonId) || document.querySelector(`button[onclick*="${buttonId}"]`);
    if (button) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// Add CSS for loading animation
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// Add real-time validation
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for real-time validation
    const fields = ['fullName', 'email', 'phone', 'dateOfBirth', 'gender', 'address', 'emergencyContactName', 'emergencyContactPhone', 'password'];

    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', function() {
                validateSingleField(fieldId);
            });

            field.addEventListener('input', function() {
                // Clear error styling on input
                highlightField(fieldId, false);
            });
        }
    });
});

function validateSingleField(fieldId) {
    const field = document.getElementById(fieldId);
    const value = field.value.trim();
    let hasError = false;

    switch (fieldId) {
        case 'fullName':
            hasError = value.length < 2;
            break;
        case 'email':
            const isUpdate = document.getElementById('formAction').value === 'update';
            if (!isUpdate) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                hasError = !emailRegex.test(value);
            }
            break;
        case 'phone':
            if (value) {
                hasError = !/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(value);
            }
            break;
        case 'dateOfBirth':
            if (value) {
                const dob = new Date(value);
                const today = new Date();
                const age = today.getFullYear() - dob.getFullYear();
                hasError = dob > today || age > 150;
            } else {
                hasError = true;
            }
            break;
        case 'gender':
            hasError = !value;
            break;
        case 'address':
            hasError = value.length < 10;
            break;
        case 'emergencyContactName':
            hasError = value.length < 2;
            break;
        case 'emergencyContactPhone':
            hasError = !/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(value);
            break;
        case 'password':
            const isUpdate2 = document.getElementById('formAction').value === 'update';
            if (!isUpdate2) {
                hasError = value.length < 6;
            }
            break;
    }

    highlightField(fieldId, hasError);
}

// Close modals when clicking outside
window.onclick = function(event) {
    const patientModal = document.getElementById('patientModal');
    const viewPatientModal = document.getElementById('viewPatientModal');

    if (event.target === patientModal) {
        closePatientModal();
    }
    if (event.target === viewPatientModal) {
        closeViewPatientModal();
    }
}
</script>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
