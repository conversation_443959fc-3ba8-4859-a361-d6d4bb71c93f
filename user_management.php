<?php
/**
 * User Management Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/User.php';

$auth = new Auth();
requireLogin();
requireRole('admin'); // Only admin can access user management

$user = new User();
$current_page = 'users';
$page_title = 'User Management';
$page_subtitle = 'Manage all system users';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create') {
            $email = sanitizeInput($_POST['email'] ?? '');
            $full_name = sanitizeInput($_POST['full_name'] ?? '');
            $role = sanitizeInput($_POST['role'] ?? '');
            $phone = sanitizeInput($_POST['phone'] ?? '');
            $password = $_POST['password'] ?? 'tempPassword123!';
            
            if (empty($email) || empty($full_name) || empty($role)) {
                $error = 'Please fill in all required fields.';
            } else {
                $result = $auth->register($email, $password, $full_name, $role);
                if ($result['success']) {
                    $message = 'User created successfully! Default password: tempPassword123!';
                } else {
                    $error = $result['message'];
                }
            }
        } elseif ($action === 'update') {
            $user_id = sanitizeInput($_POST['user_id'] ?? '');
            $data = [
                'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
                'email' => sanitizeInput($_POST['email'] ?? ''),
                'phone' => sanitizeInput($_POST['phone'] ?? '')
            ];
            
            if ($user->updateUser($user_id, $data)) {
                $message = 'User updated successfully!';
            } else {
                $error = 'Failed to update user.';
            }
        } elseif ($action === 'delete') {
            $user_id = sanitizeInput($_POST['user_id'] ?? '');
            if ($user->deleteUser($user_id)) {
                $message = 'User deleted successfully!';
            } else {
                $error = 'Failed to delete user.';
            }
        }
    }
}

// Get all users
$all_users = $user->getAllUsers();
$user_stats = $user->getUserStats();

ob_start();
?>

<div class="page-header">
    <div class="page-header-content">
        <h1><?php echo $page_title; ?></h1>
        <p><?php echo $page_subtitle; ?></p>
    </div>
    <div class="page-header-actions">
        <button class="btn btn-primary" onclick="showCreateModal()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add New User
        </button>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<!-- User Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon admin">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?php echo $user_stats['total_admins']; ?></div>
            <div class="stat-label">Administrators</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon doctor">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
            </svg>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?php echo $user_stats['total_doctors']; ?></div>
            <div class="stat-label">Doctors</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon patient">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
            </svg>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?php echo $user_stats['total_patients']; ?></div>
            <div class="stat-label">Patients</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon total">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
        </div>
        <div class="stat-content">
            <div class="stat-number"><?php echo array_sum($user_stats); ?></div>
            <div class="stat-label">Total Users</div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">All Users</h2>
        <div class="card-actions">
            <div class="search-box">
                <input type="text" id="userSearch" placeholder="Search users..." onkeyup="filterUsers()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table" id="usersTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Phone</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($all_users as $user_item): ?>
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar <?php echo $user_item['role']; ?>">
                                    <?php echo strtoupper(substr($user_item['full_name'], 0, 1)); ?>
                                </div>
                                <span><?php echo htmlspecialchars($user_item['full_name']); ?></span>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($user_item['email']); ?></td>
                        <td>
                            <span class="badge badge-<?php echo $user_item['role']; ?>">
                                <?php echo ucfirst($user_item['role']); ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($user_item['phone'] ?? 'N/A'); ?></td>
                        <td><?php echo date('M j, Y', strtotime($user_item['created_at'])); ?></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-secondary" onclick="editUser('<?php echo $user_item['id']; ?>')">
                                    Edit
                                </button>
                                <?php if ($user_item['id'] !== $_SESSION['user_id']): ?>
                                <button class="btn btn-sm btn-danger" onclick="deleteUser('<?php echo $user_item['id']; ?>')">
                                    Delete
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div id="createModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add New User</h3>
            <span class="close" onclick="closeModal('createModal')">&times;</span>
        </div>
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="create">

            <div class="form-group">
                <label for="full_name">Full Name *</label>
                <input type="text" id="full_name" name="full_name" required>
            </div>

            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="role">Role *</label>
                <select id="role" name="role" required>
                    <option value="">Select Role</option>
                    <option value="admin">Administrator</option>
                    <option value="doctor">Doctor</option>
                    <option value="patient">Patient</option>
                </select>
            </div>

            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="tempPassword123!" readonly>
                <small>Default password will be: tempPassword123!</small>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Create User</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit User</h3>
            <span class="close" onclick="closeModal('editModal')">&times;</span>
        </div>
        <form method="POST" id="editForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="user_id" id="edit_user_id">

            <div class="form-group">
                <label for="edit_full_name">Full Name</label>
                <input type="text" id="edit_full_name" name="full_name" required>
            </div>

            <div class="form-group">
                <label for="edit_email">Email Address</label>
                <input type="email" id="edit_email" name="email" required>
            </div>

            <div class="form-group">
                <label for="edit_phone">Phone Number</label>
                <input type="tel" id="edit_phone" name="phone">
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Update User</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Confirm Delete</h3>
            <span class="close" onclick="closeModal('deleteModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this user? This action cannot be undone.</p>
        </div>
        <form method="POST" id="deleteForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="user_id" id="delete_user_id">

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">Cancel</button>
                <button type="submit" class="btn btn-danger">Delete User</button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functions
function showCreateModal() {
    document.getElementById('createModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Edit user function
function editUser(userId) {
    // Find user data from the table
    const row = document.querySelector(`button[onclick="editUser('${userId}')"]`).closest('tr');
    const cells = row.querySelectorAll('td');

    document.getElementById('edit_user_id').value = userId;
    document.getElementById('edit_full_name').value = cells[0].querySelector('span').textContent;
    document.getElementById('edit_email').value = cells[1].textContent;
    document.getElementById('edit_phone').value = cells[3].textContent === 'N/A' ? '' : cells[3].textContent;

    document.getElementById('editModal').style.display = 'block';
}

// Delete user function
function deleteUser(userId) {
    document.getElementById('delete_user_id').value = userId;
    document.getElementById('deleteModal').style.display = 'block';
}

// Search function
function filterUsers() {
    const input = document.getElementById('userSearch');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('usersTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length - 1; j++) {
            if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                found = true;
                break;
            }
        }

        row.style.display = found ? '' : 'none';
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}
</script>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stat-icon.admin { background: #8b5cf6; }
.stat-icon.doctor { background: #06b6d4; }
.stat-icon.patient { background: #10b981; }
.stat-icon.total { background: #6366f1; }

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #1f2937;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
}

.user-avatar.admin { background: #8b5cf6; }
.user-avatar.doctor { background: #06b6d4; }
.user-avatar.patient { background: #10b981; }

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.badge-admin { background: #ede9fe; color: #7c3aed; }
.badge-doctor { background: #cffafe; color: #0891b2; }
.badge-patient { background: #d1fae5; color: #059669; }

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    padding-right: 2.5rem;
}

.search-box svg {
    position: absolute;
    right: 0.75rem;
    color: #6b7280;
}
</style>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
