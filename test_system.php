<?php
/**
 * System Test Page
 * Hospital Management System
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>🏥 اختبار نظام المستشفى</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #059669; background: #f0fdf4; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #dc2626; background: #fef2f2; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #2563eb; background: #eff6ff; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".btn { background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }";
echo ".btn:hover { background: #1d4ed8; }";
echo "h1, h2 { color: #111827; }";
echo "pre { background: #f9fafb; padding: 10px; border-radius: 4px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🏥 اختبار نظام إدارة المستشفى</h1>";

// Test 1: PHP Version
echo "<h2>1️⃣ اختبار PHP</h2>";
if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
    echo "<div class='success'>✅ PHP يعمل بشكل صحيح - الإصدار: " . PHP_VERSION . "</div>";
} else {
    echo "<div class='error'>❌ PHP قديم - يحتاج إصدار 7.4 أو أحدث</div>";
}

// Test 2: Directory Structure
echo "<h2>2️⃣ اختبار هيكل المجلدات</h2>";
$required_dirs = ['config', 'classes', 'assets', 'database', 'includes'];
$all_dirs_exist = true;

foreach ($required_dirs as $dir) {
    if (is_dir(__DIR__ . '/' . $dir)) {
        echo "<div class='success'>✅ مجلد $dir موجود</div>";
    } else {
        echo "<div class='error'>❌ مجلد $dir غير موجود</div>";
        $all_dirs_exist = false;
    }
}

// Test 3: Required Files
echo "<h2>3️⃣ اختبار الملفات المطلوبة</h2>";
$required_files = [
    'config/config.php',
    'config/database.php',
    'classes/Auth.php',
    'login.php',
    'dashboard.php'
];

$all_files_exist = true;
foreach ($required_files as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<div class='success'>✅ ملف $file موجود</div>";
    } else {
        echo "<div class='error'>❌ ملف $file غير موجود</div>";
        $all_files_exist = false;
    }
}

// Test 4: Configuration
echo "<h2>4️⃣ اختبار الإعدادات</h2>";
if (file_exists(__DIR__ . '/config/config.php')) {
    require_once __DIR__ . '/config/config.php';
    echo "<div class='info'>📋 BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'غير محدد') . "</div>";
    echo "<div class='info'>📋 APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'غير محدد') . "</div>";
    echo "<div class='info'>📋 DEMO_MODE: " . (defined('DEMO_MODE') && DEMO_MODE ? 'مفعل' : 'غير مفعل') . "</div>";
}

// Test 5: Database Connection
echo "<h2>5️⃣ اختبار قاعدة البيانات</h2>";
try {
    if (file_exists(__DIR__ . '/config/database.php')) {
        require_once __DIR__ . '/config/database.php';
        $database = new Database();
        $conn = $database->getConnection();
        
        if ($conn) {
            echo "<div class='success'>✅ الاتصال بقاعدة البيانات يعمل</div>";
            
            // Test tables
            $tables = ['user_profiles', 'departments', 'doctors', 'patients', 'appointments'];
            foreach ($tables as $table) {
                try {
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM $table");
                    $stmt->execute();
                    $count = $stmt->fetchColumn();
                    echo "<div class='success'>✅ جدول $table موجود ($count سجل)</div>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ جدول $table غير موجود</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ فشل الاتصال بقاعدة البيانات</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='info'>💡 يمكنك استخدام النظام في وضع التجريب بدون قاعدة بيانات</div>";
}

// Test 6: Session
echo "<h2>6️⃣ اختبار الجلسات</h2>";
if (session_status() === PHP_SESSION_ACTIVE || session_start()) {
    echo "<div class='success'>✅ الجلسات تعمل بشكل صحيح</div>";
} else {
    echo "<div class='error'>❌ مشكلة في الجلسات</div>";
}

// Test 7: Permissions
echo "<h2>7️⃣ اختبار الصلاحيات</h2>";
if (is_writable(__DIR__)) {
    echo "<div class='success'>✅ صلاحيات الكتابة متوفرة</div>";
} else {
    echo "<div class='error'>❌ صلاحيات الكتابة غير متوفرة</div>";
}

// Current Path Info
echo "<h2>8️⃣ معلومات المسار</h2>";
echo "<div class='info'>📁 المسار الحالي: " . __DIR__ . "</div>";
echo "<div class='info'>🌐 URL المتوقع: http://localhost/dashboard/php_hospital_system/</div>";
echo "<div class='info'>📄 هذا الملف: " . $_SERVER['REQUEST_URI'] . "</div>";

// Quick Links
echo "<h2>🔗 روابط سريعة</h2>";
echo "<a href='setup.php' class='btn'>🛠️ معالج الإعداد</a>";
echo "<a href='login.php' class='btn'>🔐 صفحة تسجيل الدخول</a>";
echo "<a href='dashboard.php' class='btn'>📊 لوحة التحكم</a>";

// Demo Accounts
echo "<h2>👥 الحسابات التجريبية</h2>";
echo "<div class='info'>";
echo "<strong>مدير النظام:</strong> <EMAIL> / admin123<br>";
echo "<strong>طبيب:</strong> <EMAIL> / doctor123<br>";
echo "<strong>مريض:</strong> <EMAIL> / patient123";
echo "</div>";

// System Status
echo "<h2>📊 حالة النظام</h2>";
if ($all_dirs_exist && $all_files_exist) {
    echo "<div class='success'>🎉 النظام جاهز للاستخدام!</div>";
    echo "<a href='login.php' class='btn' style='background: #059669;'>🚀 ابدأ الآن</a>";
} else {
    echo "<div class='error'>⚠️ النظام يحتاج إلى إصلاح</div>";
    echo "<a href='setup.php' class='btn' style='background: #d97706;'>🔧 تشغيل معالج الإعداد</a>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
