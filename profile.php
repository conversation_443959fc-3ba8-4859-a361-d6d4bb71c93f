<?php
/**
 * Profile Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/User.php';

$auth = new Auth();
requireLogin();

$current_user = $auth->getCurrentUser();
$user = new User();

$current_page = 'profile';
$page_title = 'Profile Settings';
$page_subtitle = 'Manage your account information';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        
        if (empty($full_name)) {
            $error = 'Full name is required.';
        } else {
            $data = [
                'full_name' => $full_name,
                'phone' => $phone
            ];
            
            if ($user->updateUser($current_user['id'], $data)) {
                // Update session data
                $_SESSION['user_name'] = $full_name;
                $current_user['full_name'] = $full_name;
                $message = 'Profile updated successfully!';
            } else {
                $error = 'Failed to update profile. Please try again.';
            }
        }
    }
}

$csrf_token = generateCSRFToken();

ob_start();
?>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="20,6 9,17 4,12"/>
        </svg>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<!-- Profile Information -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">Profile Information</h2>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" value="<?php echo htmlspecialchars($current_user['email']); ?>" disabled>
                <small style="color: #6b7280;">Email cannot be changed</small>
            </div>
            
            <div class="form-group">
                <label for="full_name">Full Name</label>
                <input type="text" id="full_name" name="full_name" 
                       value="<?php echo htmlspecialchars($current_user['full_name']); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" 
                       value="<?php echo htmlspecialchars($current_user['phone'] ?? ''); ?>">
            </div>
            
            <div class="form-group">
                <label for="role">Role</label>
                <input type="text" id="role" value="<?php echo ucfirst($current_user['role']); ?>" disabled>
                <small style="color: #6b7280;">Role cannot be changed</small>
            </div>
            
            <button type="submit" class="btn btn-primary">Update Profile</button>
        </form>
    </div>
</div>

<!-- Account Information -->
<div class="card" style="margin-top: 2rem;">
    <div class="card-header">
        <h2 class="card-title">Account Information</h2>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>
                <strong>User ID:</strong><br>
                <span style="color: #6b7280; font-family: monospace;"><?php echo htmlspecialchars($current_user['id']); ?></span>
            </div>
            <div>
                <strong>Role:</strong><br>
                <span class="badge badge-info"><?php echo ucfirst($current_user['role']); ?></span>
            </div>
            <?php if (DEMO_MODE): ?>
            <div>
                <strong>Mode:</strong><br>
                <span class="badge badge-warning">Demo Mode</span>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (DEMO_MODE): ?>
<!-- Demo Mode Notice -->
<div class="card" style="margin-top: 2rem;">
    <div class="card-body">
        <div class="alert alert-warning">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
            <strong>Demo Mode Active:</strong> This is a demonstration version. Some features may be limited and data changes may not persist.
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
