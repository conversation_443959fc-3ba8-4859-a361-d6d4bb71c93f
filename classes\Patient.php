<?php
/**
 * Patient Model Class
 * Hospital Reservation System
 */

require_once __DIR__ . '/../config/config.php';

class Patient {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all patients
     */
    public function getAllPatients() {
        try {
            $query = "SELECT 
                        p.id, p.user_id, p.medical_record_number, p.date_of_birth, 
                        p.gender, p.address, p.emergency_contact_name, p.emergency_contact_phone,
                        u.email, u.full_name, u.phone, u.created_at
                      FROM patients p
                      LEFT JOIN user_profiles u ON p.user_id = u.id
                      ORDER BY u.created_at DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get patient by ID
     */
    public function getPatientById($id) {
        try {
            $query = "SELECT 
                        p.id, p.user_id, p.medical_record_number, p.date_of_birth, 
                        p.gender, p.address, p.emergency_contact_name, p.emergency_contact_phone,
                        u.email, u.full_name, u.phone, u.created_at
                      FROM patients p
                      LEFT JOIN user_profiles u ON p.user_id = u.id
                      WHERE p.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * Create new patient
     */
    public function createPatient($userData, $patientData) {
        try {
            $this->db->beginTransaction();
            
            // Validate required fields
            $this->validateUserData($userData);
            $this->validatePatientData($patientData);
            
            // Create user profile first
            $userQuery = "INSERT INTO user_profiles (email, full_name, role, phone, password_hash) 
                         VALUES (:email, :full_name, 'patient', :phone, :password_hash)";
            $userStmt = $this->db->prepare($userQuery);
            $userStmt->bindParam(':email', $userData['email']);
            $userStmt->bindParam(':full_name', $userData['full_name']);
            $userStmt->bindParam(':phone', $userData['phone']);
            $userStmt->bindParam(':password_hash', $userData['password_hash']);
            $userStmt->execute();
            
            $userId = $this->db->lastInsertId();
            
            // Generate medical record number
            $medicalRecordNumber = $this->generateMedicalRecordNumber();
            
            // Create patient record
            $patientQuery = "INSERT INTO patients (user_id, medical_record_number, date_of_birth, gender, address, emergency_contact_name, emergency_contact_phone) 
                           VALUES (:user_id, :medical_record_number, :date_of_birth, :gender, :address, :emergency_contact_name, :emergency_contact_phone)";
            $patientStmt = $this->db->prepare($patientQuery);
            $patientStmt->bindParam(':user_id', $userId);
            $patientStmt->bindParam(':medical_record_number', $medicalRecordNumber);
            $patientStmt->bindParam(':date_of_birth', $patientData['date_of_birth']);
            $patientStmt->bindParam(':gender', $patientData['gender']);
            $patientStmt->bindParam(':address', $patientData['address']);
            $patientStmt->bindParam(':emergency_contact_name', $patientData['emergency_contact_name']);
            $patientStmt->bindParam(':emergency_contact_phone', $patientData['emergency_contact_phone']);
            $patientStmt->execute();
            
            $this->db->commit();
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Update patient
     */
    public function updatePatient($id, $userData, $patientData) {
        try {
            $this->db->beginTransaction();
            
            // Get patient's user_id
            $patient = $this->getPatientById($id);
            if (!$patient) {
                throw new Exception('Patient not found');
            }
            
            // Validate data
            $this->validateUserData($userData, true);
            $this->validatePatientData($patientData, true);
            
            // Update user profile
            $userQuery = "UPDATE user_profiles SET 
                         full_name = :full_name, 
                         phone = :phone, 
                         updated_at = CURRENT_TIMESTAMP 
                         WHERE id = :user_id";
            $userStmt = $this->db->prepare($userQuery);
            $userStmt->bindParam(':full_name', $userData['full_name']);
            $userStmt->bindParam(':phone', $userData['phone']);
            $userStmt->bindParam(':user_id', $patient['user_id']);
            $userStmt->execute();
            
            // Update patient record
            $patientQuery = "UPDATE patients SET 
                           date_of_birth = :date_of_birth,
                           gender = :gender,
                           address = :address,
                           emergency_contact_name = :emergency_contact_name,
                           emergency_contact_phone = :emergency_contact_phone,
                           updated_at = CURRENT_TIMESTAMP
                           WHERE id = :id";
            $patientStmt = $this->db->prepare($patientQuery);
            $patientStmt->bindParam(':date_of_birth', $patientData['date_of_birth']);
            $patientStmt->bindParam(':gender', $patientData['gender']);
            $patientStmt->bindParam(':address', $patientData['address']);
            $patientStmt->bindParam(':emergency_contact_name', $patientData['emergency_contact_name']);
            $patientStmt->bindParam(':emergency_contact_phone', $patientData['emergency_contact_phone']);
            $patientStmt->bindParam(':id', $id);
            $patientStmt->execute();
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Delete patient
     */
    public function deletePatient($id) {
        try {
            $this->db->beginTransaction();
            
            // Get patient's user_id
            $patient = $this->getPatientById($id);
            if (!$patient) {
                throw new Exception('Patient not found');
            }
            
            // Check if patient has appointments
            $appointmentQuery = "SELECT COUNT(*) as count FROM appointments WHERE patient_id = :patient_id";
            $appointmentStmt = $this->db->prepare($appointmentQuery);
            $appointmentStmt->bindParam(':patient_id', $id);
            $appointmentStmt->execute();
            $appointmentCount = $appointmentStmt->fetch()['count'];
            
            if ($appointmentCount > 0) {
                throw new Exception('Cannot delete patient with existing appointments');
            }
            
            // Delete patient record (this will cascade to user_profiles due to foreign key)
            $patientQuery = "DELETE FROM patients WHERE id = :id";
            $patientStmt = $this->db->prepare($patientQuery);
            $patientStmt->bindParam(':id', $id);
            $patientStmt->execute();
            
            // Delete user profile
            $userQuery = "DELETE FROM user_profiles WHERE id = :user_id";
            $userStmt = $this->db->prepare($userQuery);
            $userStmt->bindParam(':user_id', $patient['user_id']);
            $userStmt->execute();
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * Validate user data
     */
    private function validateUserData($userData, $isUpdate = false) {
        $errors = [];

        // Full name validation
        if (empty($userData['full_name']) || strlen(trim($userData['full_name'])) < 2) {
            $errors[] = 'Full name must be at least 2 characters long';
        }

        // Email validation (only for new patients)
        if (!$isUpdate) {
            if (empty($userData['email']) || !filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Valid email address is required';
            }

            // Check if email already exists
            if ($this->emailExists($userData['email'])) {
                $errors[] = 'Email address already exists';
            }

            // Password validation
            if (empty($userData['password'])) {
                $errors[] = 'Password is required';
            }
        }

        // Phone validation
        if (!empty($userData['phone']) && !preg_match('/^[\+]?[0-9\s\-\(\)]{10,15}$/', $userData['phone'])) {
            $errors[] = 'Invalid phone number format';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }

    /**
     * Validate patient data
     */
    private function validatePatientData($patientData, $isUpdate = false) {
        $errors = [];

        // Date of birth validation
        if (empty($patientData['date_of_birth'])) {
            $errors[] = 'Date of birth is required';
        } else {
            $dob = DateTime::createFromFormat('Y-m-d', $patientData['date_of_birth']);
            if (!$dob || $dob->format('Y-m-d') !== $patientData['date_of_birth']) {
                $errors[] = 'Invalid date of birth format';
            } else {
                $today = new DateTime();
                if ($dob > $today) {
                    $errors[] = 'Date of birth cannot be in the future';
                }

                $age = $today->diff($dob)->y;
                if ($age > 150) {
                    $errors[] = 'Invalid date of birth - age cannot exceed 150 years';
                }
            }
        }

        // Gender validation
        if (empty($patientData['gender']) || !in_array($patientData['gender'], ['male', 'female', 'other'])) {
            $errors[] = 'Valid gender selection is required';
        }

        // Address validation
        if (empty($patientData['address']) || strlen(trim($patientData['address'])) < 10) {
            $errors[] = 'Address must be at least 10 characters long';
        }

        // Emergency contact validation
        if (empty($patientData['emergency_contact_name']) || strlen(trim($patientData['emergency_contact_name'])) < 2) {
            $errors[] = 'Emergency contact name must be at least 2 characters long';
        }

        if (empty($patientData['emergency_contact_phone']) || !preg_match('/^[\+]?[0-9\s\-\(\)]{10,15}$/', $patientData['emergency_contact_phone'])) {
            $errors[] = 'Valid emergency contact phone number is required';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }

    /**
     * Check if email exists
     */
    private function emailExists($email) {
        try {
            $query = "SELECT COUNT(*) as count FROM user_profiles WHERE email = :email";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();

            return $stmt->fetch()['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Generate medical record number
     */
    private function generateMedicalRecordNumber() {
        $prefix = 'MRN';
        $year = date('Y');

        try {
            // Get the last medical record number for this year
            $query = "SELECT medical_record_number FROM patients
                     WHERE medical_record_number LIKE :pattern
                     ORDER BY medical_record_number DESC LIMIT 1";
            $pattern = $prefix . $year . '%';
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':pattern', $pattern);
            $stmt->execute();

            $lastRecord = $stmt->fetch();

            if ($lastRecord) {
                // Extract the sequence number and increment
                $lastNumber = substr($lastRecord['medical_record_number'], -4);
                $nextNumber = str_pad((int)$lastNumber + 1, 4, '0', STR_PAD_LEFT);
            } else {
                // First record for this year
                $nextNumber = '0001';
            }

            return $prefix . $year . $nextNumber;
        } catch (PDOException $e) {
            // Fallback to timestamp-based number
            return $prefix . $year . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Get patient statistics
     */
    public function getPatientStats() {
        try {
            $stats = [];

            // Total patients
            $query = "SELECT COUNT(*) as count FROM patients";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['total_patients'] = $stmt->fetch()['count'];

            // Patients by gender
            $query = "SELECT gender, COUNT(*) as count FROM patients GROUP BY gender";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $genderStats = $stmt->fetchAll();

            foreach ($genderStats as $stat) {
                $stats['patients_' . $stat['gender']] = $stat['count'];
            }

            // New patients this month
            $query = "SELECT COUNT(*) as count FROM patients WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['new_patients_this_month'] = $stmt->fetch()['count'];

            return $stats;
        } catch (PDOException $e) {
            return [
                'total_patients' => 0,
                'patients_male' => 0,
                'patients_female' => 0,
                'patients_other' => 0,
                'new_patients_this_month' => 0
            ];
        }
    }
}
?>
