<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <!-- Gradients -->
    <radialGradient id="mainGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </radialGradient>

    <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <radialGradient id="heartGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f87171;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </radialGradient>

    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>

  <!-- Main circle with shadow -->
  <circle cx="100" cy="100" r="85" fill="url(#mainGradient)" filter="url(#shadow)"/>

  <!-- Inner white circle -->
  <circle cx="100" cy="100" r="70" fill="#ffffff" stroke="#e5e7eb" stroke-width="1"/>

  <!-- Medical cross with rounded corners -->
  <g transform="translate(100,100)">
    <!-- Vertical bar -->
    <rect x="-6" y="-30" width="12" height="60" rx="6" fill="url(#crossGradient)" stroke="#3b82f6" stroke-width="1"/>
    <!-- Horizontal bar -->
    <rect x="-30" y="-6" width="60" height="12" rx="6" fill="url(#crossGradient)" stroke="#3b82f6" stroke-width="1"/>
  </g>

  <!-- Heart symbol (top left) -->
  <g transform="translate(70,70)">
    <path d="M0,5 C0,2 2,0 5,0 C7,0 8,1 9,2 C10,1 11,0 13,0 C16,0 18,2 18,5 C18,8 9,15 9,15 S0,8 0,5 Z"
          fill="url(#heartGradient)" stroke="#ffffff" stroke-width="1"/>
  </g>

  <!-- DNA helix (top right) -->
  <g transform="translate(130,70)">
    <path d="M2,0 Q8,5 2,10 Q8,15 2,20" stroke="#10b981" stroke-width="2" fill="none"/>
    <path d="M8,0 Q2,5 8,10 Q2,15 8,20" stroke="#10b981" stroke-width="2" fill="none"/>
    <circle cx="2" cy="5" r="1.5" fill="#10b981"/>
    <circle cx="8" cy="5" r="1.5" fill="#10b981"/>
    <circle cx="2" cy="15" r="1.5" fill="#10b981"/>
    <circle cx="8" cy="15" r="1.5" fill="#10b981"/>
  </g>

  <!-- Stethoscope (bottom left) -->
  <g transform="translate(70,130)">
    <circle cx="3" cy="3" r="2" fill="none" stroke="#8b5cf6" stroke-width="2"/>
    <path d="M3,5 Q3,10 8,10 Q13,10 13,5" fill="none" stroke="#8b5cf6" stroke-width="2"/>
    <circle cx="13" cy="5" r="1.5" fill="#8b5cf6"/>
  </g>

  <!-- Medical pill (bottom right) -->
  <g transform="translate(130,130)">
    <ellipse cx="5" cy="5" rx="4" ry="8" fill="#f59e0b" stroke="#ffffff" stroke-width="1" transform="rotate(45 5 5)"/>
    <ellipse cx="5" cy="5" rx="4" ry="4" fill="#ffffff" transform="rotate(45 5 5)"/>
  </g>

  <!-- Central highlight -->
  <circle cx="100" cy="100" r="8" fill="#3b82f6" opacity="0.8"/>
  <circle cx="100" cy="100" r="4" fill="#ffffff"/>
</svg>
