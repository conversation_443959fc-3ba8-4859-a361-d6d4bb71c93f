<?php
/**
 * Appointments Management Page
 * Hospital Reservation System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/Appointment.php';
require_once 'classes/Patient.php';
require_once 'classes/Doctor.php';

$auth = new Auth();
requireLogin();

$current_user = $auth->getCurrentUser();
$appointment = new Appointment();
$patient = new Patient();
$doctor = new Doctor();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'create':
                $appointmentData = [
                    'patient_id' => $_POST['patient_id'],
                    'doctor_id' => $_POST['doctor_id'],
                    'appointment_date' => $_POST['appointment_date'],
                    'appointment_time' => $_POST['appointment_time'],
                    'status' => $_POST['status'] ?? 'scheduled',
                    'notes' => $_POST['notes'] ?? ''
                ];

                $appointmentId = $appointment->createAppointment($appointmentData);
                echo json_encode(['success' => true, 'message' => 'Appointment created successfully', 'appointment_id' => $appointmentId]);
                break;

            case 'update':
                $appointmentData = [
                    'patient_id' => $_POST['patient_id'],
                    'doctor_id' => $_POST['doctor_id'],
                    'appointment_date' => $_POST['appointment_date'],
                    'appointment_time' => $_POST['appointment_time'],
                    'status' => $_POST['status'],
                    'notes' => $_POST['notes'] ?? ''
                ];

                $appointment->updateAppointment($_POST['appointment_id'], $appointmentData);
                echo json_encode(['success' => true, 'message' => 'Appointment updated successfully']);
                break;

            case 'delete':
                $appointment->deleteAppointment($_POST['appointment_id']);
                echo json_encode(['success' => true, 'message' => 'Appointment deleted successfully']);
                break;

            case 'get':
                $appointmentData = $appointment->getAppointmentById($_POST['appointment_id']);
                if ($appointmentData) {
                    echo json_encode(['success' => true, 'appointment' => $appointmentData]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Appointment not found']);
                }
                break;

            case 'update_status':
                $appointment->updateAppointmentStatus($_POST['appointment_id'], $_POST['status']);
                echo json_encode(['success' => true, 'message' => 'Appointment status updated successfully']);
                break;

            case 'get_patients':
                $patients = $patient->getAllPatients();
                echo json_encode(['success' => true, 'patients' => $patients]);
                break;

            case 'get_doctors':
                $doctors = $doctor->getAllDoctors();
                echo json_encode(['success' => true, 'doctors' => $doctors]);
                break;

            case 'get_available_slots':
                $slots = $appointment->getAvailableTimeSlots($_POST['doctor_id'], $_POST['date']);
                echo json_encode(['success' => true, 'slots' => $slots]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

$appointments = $appointment->getAllAppointments(50); // Get more appointments

$current_page = 'appointments';
$page_title = 'Appointments Management';
$page_subtitle = 'View and manage appointments';

function getStatusBadge($status) {
    $badges = [
        'scheduled' => 'badge-info',
        'completed' => 'badge-success',
        'cancelled' => 'badge-danger'
    ];
    
    return $badges[$status] ?? 'badge-info';
}

function formatTime($time) {
    return date('g:i A', strtotime($time));
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}

ob_start();
?>

<!-- Appointments Table -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 class="card-title">All Appointments (<?php echo count($appointments); ?>)</h2>
            <?php if ($current_user['role'] === 'admin'): ?>
                <button type="button" class="btn btn-primary" onclick="openAddAppointmentModal()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    Schedule Appointment
                </button>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($appointments)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">No appointments found.</p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Patient</th>
                            <th>Doctor</th>
                            <th>Specialization</th>
                            <th>Status</th>
                            <th>Notes</th>
                            <?php if ($current_user['role'] === 'admin'): ?>
                                <th>Actions</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td><?php echo formatDate($appointment['appointment_date']); ?></td>
                                <td><?php echo formatTime($appointment['appointment_time']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['patient_name']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['doctor_name']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['specialization']); ?></td>
                                <td>
                                    <span class="badge <?php echo getStatusBadge($appointment['status']); ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($appointment['notes'] ?? '-'); ?></td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem;">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="viewAppointment('<?php echo $appointment['id']; ?>')">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                                <circle cx="12" cy="12" r="3"/>
                                            </svg>
                                        </button>
                                        <?php if ($current_user['role'] === 'admin'): ?>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="editAppointment('<?php echo $appointment['id']; ?>')">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>
                                            <?php if ($appointment['status'] !== 'completed'): ?>
                                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteAppointment('<?php echo $appointment['id']; ?>', '<?php echo htmlspecialchars($appointment['patient_name']); ?>')">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <polyline points="3,6 5,6 21,6"/>
                                                        <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"/>
                                                    </svg>
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($appointment['status'] === 'scheduled'): ?>
                                                <button type="button" class="btn btn-success btn-sm" onclick="updateAppointmentStatus('<?php echo $appointment['id']; ?>', 'completed')">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <polyline points="20,6 9,17 4,12"/>
                                                    </svg>
                                                </button>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Appointment Modal -->
<div id="appointmentModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="appointmentModalTitle">Schedule Appointment</h3>
            <span class="close" onclick="closeAppointmentModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="appointmentForm">
                <input type="hidden" id="appointmentId" name="appointment_id">
                <input type="hidden" id="appointmentAction" name="action" value="create">

                <div class="form-row">
                    <div class="form-group">
                        <label for="patientSelect">Patient *</label>
                        <select id="patientSelect" name="patient_id" required>
                            <option value="">Select Patient</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="doctorSelect">Doctor *</label>
                        <select id="doctorSelect" name="doctor_id" required onchange="updateAvailableSlots()">
                            <option value="">Select Doctor</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="appointmentDate">Date *</label>
                        <input type="date" id="appointmentDate" name="appointment_date" required onchange="updateAvailableSlots()">
                    </div>
                    <div class="form-group">
                        <label for="appointmentTime">Time *</label>
                        <select id="appointmentTime" name="appointment_time" required>
                            <option value="">Select Time</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="appointmentStatus">Status</label>
                        <select id="appointmentStatus" name="status">
                            <option value="scheduled">Scheduled</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="appointmentNotes">Notes</label>
                    <textarea id="appointmentNotes" name="notes" rows="3" placeholder="Additional notes or comments..."></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeAppointmentModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="saveAppointment()">Save Appointment</button>
        </div>
    </div>
</div>

<!-- View Appointment Modal -->
<div id="viewAppointmentModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Appointment Details</h3>
            <span class="close" onclick="closeViewAppointmentModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="appointmentDetails"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeViewAppointmentModal()">Close</button>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
}

.close {
    color: #6b7280;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    flex: 1;
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.appointment-detail-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 4px;
}

.appointment-detail-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.appointment-detail-value {
    color: #6b7280;
}
</style>

<script>
// Load patients and doctors when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadPatients();
    loadDoctors();
    setMinDate();
    setupAppointmentValidation();
});

function setupAppointmentValidation() {
    // Add event listeners for real-time validation
    const fields = ['patientSelect', 'doctorSelect', 'appointmentDate', 'appointmentTime', 'appointmentNotes'];

    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', function() {
                validateSingleAppointmentField(fieldId);
            });

            field.addEventListener('change', function() {
                validateSingleAppointmentField(fieldId);
            });

            field.addEventListener('input', function() {
                // Clear error styling on input
                highlightAppointmentField(fieldId, false);
            });
        }
    });
}

function validateSingleAppointmentField(fieldId) {
    const field = document.getElementById(fieldId);
    const value = field.value.trim();
    let hasError = false;

    switch (fieldId) {
        case 'patientSelect':
            hasError = !value;
            break;
        case 'doctorSelect':
            hasError = !value;
            break;
        case 'appointmentDate':
            if (!value) {
                hasError = true;
            } else {
                const selectedDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const maxDate = new Date();
                maxDate.setFullYear(maxDate.getFullYear() + 1);
                hasError = selectedDate < today || selectedDate > maxDate;
            }
            break;
        case 'appointmentTime':
            if (!value) {
                hasError = true;
            } else {
                const [hours, minutes] = value.split(':');
                const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);
                const startTime = 8 * 60; // 8:00 AM
                const endTime = 18 * 60; // 6:00 PM
                hasError = timeInMinutes < startTime || timeInMinutes > endTime;
            }
            break;
        case 'appointmentNotes':
            hasError = value.length > 500;
            break;
    }

    highlightAppointmentField(fieldId, hasError);
}

function loadPatients() {
    fetch('appointments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_patients'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('patientSelect');
            select.innerHTML = '<option value="">Select Patient</option>';
            data.patients.forEach(patient => {
                const option = document.createElement('option');
                option.value = patient.id;
                option.textContent = `${patient.full_name} (${patient.medical_record_number})`;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading patients:', error);
    });
}

function loadDoctors() {
    fetch('appointments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_doctors'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('doctorSelect');
            select.innerHTML = '<option value="">Select Doctor</option>';
            data.doctors.forEach(doctor => {
                const option = document.createElement('option');
                option.value = doctor.id;
                option.textContent = `Dr. ${doctor.full_name} - ${doctor.specialization}`;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading doctors:', error);
    });
}

function setMinDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('appointmentDate').min = today;
}

function updateAvailableSlots() {
    const doctorId = document.getElementById('doctorSelect').value;
    const date = document.getElementById('appointmentDate').value;

    if (doctorId && date) {
        fetch('appointments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_available_slots&doctor_id=${doctorId}&date=${date}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('appointmentTime');
                select.innerHTML = '<option value="">Select Time</option>';
                data.slots.forEach(slot => {
                    const option = document.createElement('option');
                    option.value = slot;
                    option.textContent = formatTime(slot);
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading available slots:', error);
        });
    }
}

function formatTime(time) {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
}

function openAddAppointmentModal() {
    document.getElementById('appointmentModalTitle').textContent = 'Schedule Appointment';
    document.getElementById('appointmentAction').value = 'create';
    document.getElementById('appointmentId').value = '';
    document.getElementById('appointmentForm').reset();
    document.getElementById('appointmentStatus').value = 'scheduled';
    setMinDate();
    document.getElementById('appointmentModal').style.display = 'block';
}

function editAppointment(appointmentId) {
    fetch('appointments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get&appointment_id=' + appointmentId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const appointment = data.appointment;
            document.getElementById('appointmentModalTitle').textContent = 'Edit Appointment';
            document.getElementById('appointmentAction').value = 'update';
            document.getElementById('appointmentId').value = appointment.id;
            document.getElementById('patientSelect').value = appointment.patient_id;
            document.getElementById('doctorSelect').value = appointment.doctor_id;
            document.getElementById('appointmentDate').value = appointment.appointment_date;
            document.getElementById('appointmentStatus').value = appointment.status;
            document.getElementById('appointmentNotes').value = appointment.notes || '';

            // Load available slots and set the current time
            updateAvailableSlots();
            setTimeout(() => {
                document.getElementById('appointmentTime').value = appointment.appointment_time;
            }, 500);

            document.getElementById('appointmentModal').style.display = 'block';
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error loading appointment data', 'error');
    });
}

function viewAppointment(appointmentId) {
    fetch('appointments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get&appointment_id=' + appointmentId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const appointment = data.appointment;
            const detailsHtml = `
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Patient</div>
                    <div class="appointment-detail-value">${appointment.patient_name}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Doctor</div>
                    <div class="appointment-detail-value">Dr. ${appointment.doctor_name}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Specialization</div>
                    <div class="appointment-detail-value">${appointment.specialization}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Date</div>
                    <div class="appointment-detail-value">${new Date(appointment.appointment_date).toLocaleDateString()}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Time</div>
                    <div class="appointment-detail-value">${formatTime(appointment.appointment_time)}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Status</div>
                    <div class="appointment-detail-value">${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Notes</div>
                    <div class="appointment-detail-value">${appointment.notes || 'No notes'}</div>
                </div>
                <div class="appointment-detail-item">
                    <div class="appointment-detail-label">Created</div>
                    <div class="appointment-detail-value">${new Date(appointment.created_at).toLocaleDateString()}</div>
                </div>
            `;
            document.getElementById('appointmentDetails').innerHTML = detailsHtml;
            document.getElementById('viewAppointmentModal').style.display = 'block';
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error loading appointment data', 'error');
    });
}

function deleteAppointment(appointmentId, patientName) {
    if (confirm(`Are you sure you want to delete the appointment for "${patientName}"? This action cannot be undone.`)) {
        fetch('appointments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&appointment_id=' + appointmentId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('Error deleting appointment', 'error');
        });
    }
}

function updateAppointmentStatus(appointmentId, status) {
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);
    if (confirm(`Mark this appointment as ${statusText}?`)) {
        fetch('appointments.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=update_status&appointment_id=${appointmentId}&status=${status}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('Error updating appointment status', 'error');
        });
    }
}

function saveAppointment() {
    // Validate form before submission
    if (!validateAppointmentForm()) {
        return;
    }

    const form = document.getElementById('appointmentForm');
    const formData = new FormData(form);

    // Convert FormData to URLSearchParams for proper encoding
    const params = new URLSearchParams();
    for (const [key, value] of formData.entries()) {
        params.append(key, value);
    }

    fetch('appointments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            closeAppointmentModal();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('Error saving appointment', 'error');
    });
}

function closeAppointmentModal() {
    document.getElementById('appointmentModal').style.display = 'none';
    document.getElementById('appointmentForm').reset();
}

function closeViewAppointmentModal() {
    document.getElementById('viewAppointmentModal').style.display = 'none';
}

function validateAppointmentForm() {
    const errors = [];

    // Patient validation
    const patientId = document.getElementById('patientSelect').value;
    if (!patientId) {
        errors.push('Please select a patient');
        highlightAppointmentField('patientSelect', true);
    } else {
        highlightAppointmentField('patientSelect', false);
    }

    // Doctor validation
    const doctorId = document.getElementById('doctorSelect').value;
    if (!doctorId) {
        errors.push('Please select a doctor');
        highlightAppointmentField('doctorSelect', true);
    } else {
        highlightAppointmentField('doctorSelect', false);
    }

    // Date validation
    const appointmentDate = document.getElementById('appointmentDate').value;
    if (!appointmentDate) {
        errors.push('Please select an appointment date');
        highlightAppointmentField('appointmentDate', true);
    } else {
        const selectedDate = new Date(appointmentDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            errors.push('Appointment date cannot be in the past');
            highlightAppointmentField('appointmentDate', true);
        } else {
            const maxDate = new Date();
            maxDate.setFullYear(maxDate.getFullYear() + 1);

            if (selectedDate > maxDate) {
                errors.push('Appointment date cannot be more than 1 year in the future');
                highlightAppointmentField('appointmentDate', true);
            } else {
                highlightAppointmentField('appointmentDate', false);
            }
        }
    }

    // Time validation
    const appointmentTime = document.getElementById('appointmentTime').value;
    if (!appointmentTime) {
        errors.push('Please select an appointment time');
        highlightAppointmentField('appointmentTime', true);
    } else {
        // Check business hours (8 AM to 6 PM)
        const [hours, minutes] = appointmentTime.split(':');
        const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);
        const startTime = 8 * 60; // 8:00 AM
        const endTime = 18 * 60; // 6:00 PM

        if (timeInMinutes < startTime || timeInMinutes > endTime) {
            errors.push('Appointment time must be between 8:00 AM and 6:00 PM');
            highlightAppointmentField('appointmentTime', true);
        } else {
            highlightAppointmentField('appointmentTime', false);
        }
    }

    // Notes validation (optional but limit length)
    const notes = document.getElementById('appointmentNotes').value;
    if (notes && notes.length > 500) {
        errors.push('Notes cannot exceed 500 characters');
        highlightAppointmentField('appointmentNotes', true);
    } else {
        highlightAppointmentField('appointmentNotes', false);
    }

    if (errors.length > 0) {
        showAlert('Please fix the following errors:\n• ' + errors.join('\n• '), 'error');
        return false;
    }

    return true;
}

function highlightAppointmentField(fieldId, hasError) {
    const field = document.getElementById(fieldId);
    if (hasError) {
        field.style.borderColor = '#ef4444';
        field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
    } else {
        field.style.borderColor = '#d1d5db';
        field.style.boxShadow = 'none';
    }
}

// Alert function
function showAlert(message, type) {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        ${type === 'success' ? 'background-color: #10b981;' : 'background-color: #ef4444;'}
    `;
    alert.textContent = message;

    document.body.appendChild(alert);

    // Remove alert after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 5000);
}

// Loading indicator functions
function showLoading(buttonId) {
    const button = document.getElementById(buttonId) || document.querySelector(`button[onclick*="${buttonId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<span style="display: inline-block; width: 16px; height: 16px; border: 2px solid #ffffff; border-radius: 50%; border-top-color: transparent; animation: spin 1s linear infinite;"></span> Loading...';
    }
}

function hideLoading(buttonId, originalText) {
    const button = document.getElementById(buttonId) || document.querySelector(`button[onclick*="${buttonId}"]`);
    if (button) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// Add CSS for loading animation
const appointmentStyle = document.createElement('style');
appointmentStyle.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(appointmentStyle);

// Close modals when clicking outside
window.onclick = function(event) {
    const appointmentModal = document.getElementById('appointmentModal');
    const viewAppointmentModal = document.getElementById('viewAppointmentModal');

    if (event.target === appointmentModal) {
        closeAppointmentModal();
    }
    if (event.target === viewAppointmentModal) {
        closeViewAppointmentModal();
    }
}
</script>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
