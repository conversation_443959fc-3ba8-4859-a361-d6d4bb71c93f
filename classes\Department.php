<?php
/**
 * Department Model Class
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';

class Department {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all departments
     */
    public function getAllDepartments() {
        try {
            $query = "SELECT id, name, description, created_at FROM departments ORDER BY name";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get department by ID
     */
    public function getDepartmentById($id) {
        try {
            $query = "SELECT id, name, description, created_at FROM departments WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * Create new department
     */
    public function createDepartment($name, $description = '') {
        try {
            $query = "INSERT INTO departments (name, description) VALUES (:name, :description)";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Update department
     */
    public function updateDepartment($id, $name, $description = '') {
        try {
            $query = "UPDATE departments SET name = :name, description = :description, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Delete department
     */
    public function deleteDepartment($id) {
        try {
            $query = "DELETE FROM departments WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Get department statistics
     */
    public function getDepartmentStats() {
        try {
            $query = "SELECT 
                        d.id, d.name,
                        COUNT(doc.id) as doctor_count
                      FROM departments d
                      LEFT JOIN doctors doc ON d.id = doc.department_id
                      GROUP BY d.id, d.name
                      ORDER BY d.name";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
}
?>
