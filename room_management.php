<?php
/**
 * Room Management Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';

$auth = new Auth();
requireLogin();
requireRole('admin');

$current_page = 'rooms';
$page_title = 'Room Management';
$page_subtitle = 'Manage hospital rooms and assignments';

// Demo rooms data
$rooms = [
    ['id' => 'room-001', 'room_number' => '101', 'room_type' => 'General Ward', 'capacity' => 2, 'is_available' => true],
    ['id' => 'room-002', 'room_number' => '102', 'room_type' => 'General Ward', 'capacity' => 2, 'is_available' => true],
    ['id' => 'room-003', 'room_number' => '201', 'room_type' => 'Private Room', 'capacity' => 1, 'is_available' => false],
    ['id' => 'room-004', 'room_number' => '202', 'room_type' => 'Private Room', 'capacity' => 1, 'is_available' => true],
    ['id' => 'room-005', 'room_number' => '301', 'room_type' => 'ICU', 'capacity' => 1, 'is_available' => true],
    ['id' => 'room-006', 'room_number' => '302', 'room_type' => 'ICU', 'capacity' => 1, 'is_available' => false],
];

ob_start();
?>

<!-- Rooms Table -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 class="card-title">Hospital Rooms (<?php echo count($rooms); ?>)</h2>
            <button type="button" class="btn btn-primary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
                Add Room
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Room Number</th>
                        <th>Type</th>
                        <th>Capacity</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rooms as $room): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($room['room_number']); ?></td>
                            <td><?php echo htmlspecialchars($room['room_type']); ?></td>
                            <td><?php echo $room['capacity']; ?> bed<?php echo $room['capacity'] > 1 ? 's' : ''; ?></td>
                            <td>
                                <span class="badge <?php echo $room['is_available'] ? 'badge-success' : 'badge-danger'; ?>">
                                    <?php echo $room['is_available'] ? 'Available' : 'Occupied'; ?>
                                </span>
                            </td>
                            <td>
                                <div style="display: flex; gap: 0.5rem;">
                                    <button type="button" class="btn btn-secondary btn-sm">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                        </svg>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="3,6 5,6 21,6"/>
                                            <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Room Statistics -->
<div class="stats-grid" style="margin-top: 2rem;">
    <div class="stat-card">
        <div class="stat-icon green">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"/>
            </svg>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($rooms, function($r) { return $r['is_available']; })); ?></h3>
            <p>Available Rooms</p>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon red">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($rooms, function($r) { return !$r['is_available']; })); ?></h3>
            <p>Occupied Rooms</p>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon blue">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 4v16"/>
                <path d="M2 8h18a2 2 0 0 1 2 2v10"/>
                <path d="M2 17h20"/>
                <path d="M6 8v9"/>
            </svg>
        </div>
        <div class="stat-content">
            <h3><?php echo array_sum(array_column($rooms, 'capacity')); ?></h3>
            <p>Total Capacity</p>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
