<?php
/**
 * Dashboard Model Class
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';

class Dashboard {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get dashboard statistics
     */
    public function getStats() {
        if (DEMO_MODE) {
            return $this->getDemoStats();
        }
        
        try {
            $stats = [];
            
            // Total patients
            $query = "SELECT COUNT(*) as count FROM user_profiles WHERE role = 'patient'";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['total_patients'] = $result['count'];
            
            // Total doctors
            $query = "SELECT COUNT(*) as count FROM user_profiles WHERE role = 'doctor'";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['total_doctors'] = $result['count'];
            
            // Total appointments
            $query = "SELECT COUNT(*) as count FROM appointments";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['total_appointments'] = $result['count'];
            
            // Total departments
            $query = "SELECT COUNT(*) as count FROM departments";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['total_departments'] = $result['count'];
            
            // Today's appointments
            $today = date('Y-m-d');
            $query = "SELECT COUNT(*) as count FROM appointments WHERE appointment_date = :today";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':today', $today);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['today_appointments'] = $result['count'];
            
            // Completed appointments
            $query = "SELECT COUNT(*) as count FROM appointments WHERE status = 'completed'";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['completed_appointments'] = $result['count'];
            
            // Cancelled appointments
            $query = "SELECT COUNT(*) as count FROM appointments WHERE status = 'cancelled'";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats['cancelled_appointments'] = $result['count'];
            
            return $stats;
        } catch (PDOException $e) {
            return $this->getDemoStats();
        }
    }
    
    /**
     * Get demo statistics
     */
    private function getDemoStats() {
        return [
            'total_patients' => 3,
            'total_doctors' => 3,
            'total_appointments' => 3,
            'total_departments' => 5,
            'today_appointments' => 1,
            'completed_appointments' => 1,
            'cancelled_appointments' => 0
        ];
    }
    
    /**
     * Get recent appointments
     */
    public function getRecentAppointments($limit = 5) {
        if (DEMO_MODE) {
            return $this->getDemoAppointments();
        }
        
        try {
            $query = "SELECT 
                        a.id, a.appointment_date, a.appointment_time, a.status, a.notes,
                        p_user.full_name as patient_name,
                        d_user.full_name as doctor_name,
                        d.specialization
                      FROM appointments a
                      LEFT JOIN patients p ON a.patient_id = p.id
                      LEFT JOIN user_profiles p_user ON p.user_id = p_user.id
                      LEFT JOIN doctors d ON a.doctor_id = d.id
                      LEFT JOIN user_profiles d_user ON d.user_id = d_user.id
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC
                      LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return $this->getDemoAppointments();
        }
    }
    
    /**
     * Get demo appointments
     */
    private function getDemoAppointments() {
        return [
            [
                'id' => 'app-001',
                'appointment_date' => date('Y-m-d'),
                'appointment_time' => '09:00:00',
                'status' => 'scheduled',
                'notes' => 'Regular checkup',
                'patient_name' => 'Jane Doe',
                'doctor_name' => 'Dr. John Smith',
                'specialization' => 'Interventional Cardiology'
            ],
            [
                'id' => 'app-002',
                'appointment_date' => date('Y-m-d', strtotime('+1 day')),
                'appointment_time' => '14:30:00',
                'status' => 'scheduled',
                'notes' => 'Follow-up consultation',
                'patient_name' => 'John Patient',
                'doctor_name' => 'Dr. Sarah Wilson',
                'specialization' => 'Pediatric Neurology'
            ],
            [
                'id' => 'app-003',
                'appointment_date' => date('Y-m-d', strtotime('-1 day')),
                'appointment_time' => '11:00:00',
                'status' => 'completed',
                'notes' => 'Sports injury assessment',
                'patient_name' => 'Mary Patient',
                'doctor_name' => 'Dr. Michael Brown',
                'specialization' => 'Sports Medicine'
            ]
        ];
    }
}
?>
