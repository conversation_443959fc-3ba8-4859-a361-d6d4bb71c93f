<?php
/**
 * Quick Setup for Hospital Management System
 * This script creates the database and imports data automatically
 */

echo "<h1>🏥 EMU Hospital System - Quick Setup</h1>";
echo "<p>Setting up your database...</p>";

try {
    // Database connection settings
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'hospital_management';
    
    echo "<p>✅ Connecting to MySQL server...</p>";
    
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to MySQL server successfully!</p>";
    
    // Create database if it doesn't exist
    echo "<p>🔧 Creating database '$dbname'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname`");
    $pdo->exec("USE `$dbname`");
    
    echo "<p>✅ Database '$dbname' created successfully!</p>";
    
    // Import schema
    echo "<p>📋 Importing database schema...</p>";
    $schema = file_get_contents('database/schema.sql');
    if ($schema) {
        $pdo->exec($schema);
        echo "<p>✅ Database schema imported successfully!</p>";
    } else {
        throw new Exception("Could not read schema.sql file");
    }
    
    // Import sample data
    echo "<p>📊 Importing sample data...</p>";
    $sample_data = file_get_contents('database/sample_data.sql');
    if ($sample_data) {
        $pdo->exec($sample_data);
        echo "<p>✅ Sample data imported successfully!</p>";
    } else {
        throw new Exception("Could not read sample_data.sql file");
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<p>Your EMU Hospital System is now ready to use!</p>";
    echo "<p><strong>Demo Accounts:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / admin123</li>";
    echo "<li><strong>Doctor:</strong> <EMAIL> / doctor123</li>";
    echo "<li><strong>Patient:</strong> <EMAIL> / patient123</li>";
    echo "</ul>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Database Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Common solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check if MySQL port 3306 is available</li>";
    echo "<li>Verify database credentials</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Setup Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h1 {
    color: #2c3e50;
    text-align: center;
}
p {
    background: white;
    padding: 10px;
    border-left: 4px solid #007bff;
    margin: 10px 0;
}
</style>
