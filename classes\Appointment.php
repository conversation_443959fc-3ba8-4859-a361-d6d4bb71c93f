<?php
/**
 * Appointment Model Class
 * Hospital Reservation System
 */

require_once __DIR__ . '/../config/config.php';

class Appointment {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all appointments
     */
    public function getAllAppointments($limit = null) {
        try {
            $query = "SELECT 
                        a.id, a.appointment_date, a.appointment_time, a.status, a.notes, a.created_at,
                        p_user.full_name as patient_name, p_user.email as patient_email,
                        d_user.full_name as doctor_name, d_user.email as doctor_email,
                        d.specialization, dept.name as department_name
                      FROM appointments a
                      LEFT JOIN patients p ON a.patient_id = p.id
                      LEFT JOIN user_profiles p_user ON p.user_id = p_user.id
                      LEFT JOIN doctors d ON a.doctor_id = d.id
                      LEFT JOIN user_profiles d_user ON d.user_id = d_user.id
                      LEFT JOIN departments dept ON d.department_id = dept.id
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
            
            if ($limit) {
                $query .= " LIMIT :limit";
            }
            
            $stmt = $this->db->prepare($query);
            if ($limit) {
                $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            }
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get appointment by ID
     */
    public function getAppointmentById($id) {
        try {
            $query = "SELECT 
                        a.id, a.patient_id, a.doctor_id, a.appointment_date, a.appointment_time, 
                        a.status, a.notes, a.created_at,
                        p_user.full_name as patient_name, p_user.email as patient_email, p_user.phone as patient_phone,
                        d_user.full_name as doctor_name, d_user.email as doctor_email,
                        d.specialization, dept.name as department_name
                      FROM appointments a
                      LEFT JOIN patients p ON a.patient_id = p.id
                      LEFT JOIN user_profiles p_user ON p.user_id = p_user.id
                      LEFT JOIN doctors d ON a.doctor_id = d.id
                      LEFT JOIN user_profiles d_user ON d.user_id = d_user.id
                      LEFT JOIN departments dept ON d.department_id = dept.id
                      WHERE a.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * Create new appointment
     */
    public function createAppointment($appointmentData) {
        try {
            $this->db->beginTransaction();
            
            // Validate appointment data
            $this->validateAppointmentData($appointmentData);
            
            // Check for conflicts
            $this->checkAppointmentConflicts($appointmentData['doctor_id'], $appointmentData['appointment_date'], $appointmentData['appointment_time']);
            
            $query = "INSERT INTO appointments (patient_id, doctor_id, appointment_date, appointment_time, status, notes) 
                     VALUES (:patient_id, :doctor_id, :appointment_date, :appointment_time, :status, :notes)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':patient_id', $appointmentData['patient_id']);
            $stmt->bindParam(':doctor_id', $appointmentData['doctor_id']);
            $stmt->bindParam(':appointment_date', $appointmentData['appointment_date']);
            $stmt->bindParam(':appointment_time', $appointmentData['appointment_time']);
            $stmt->bindParam(':status', $appointmentData['status']);
            $stmt->bindParam(':notes', $appointmentData['notes']);
            $stmt->execute();
            
            $appointmentId = $this->db->lastInsertId();
            $this->db->commit();
            
            return $appointmentId;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Update appointment
     */
    public function updateAppointment($id, $appointmentData) {
        try {
            $this->db->beginTransaction();
            
            // Check if appointment exists
            $appointment = $this->getAppointmentById($id);
            if (!$appointment) {
                throw new Exception('Appointment not found');
            }
            
            // Validate appointment data
            $this->validateAppointmentData($appointmentData, true);
            
            // Check for conflicts (excluding current appointment)
            $this->checkAppointmentConflicts($appointmentData['doctor_id'], $appointmentData['appointment_date'], $appointmentData['appointment_time'], $id);
            
            $query = "UPDATE appointments SET 
                     patient_id = :patient_id,
                     doctor_id = :doctor_id,
                     appointment_date = :appointment_date,
                     appointment_time = :appointment_time,
                     status = :status,
                     notes = :notes,
                     updated_at = CURRENT_TIMESTAMP
                     WHERE id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':patient_id', $appointmentData['patient_id']);
            $stmt->bindParam(':doctor_id', $appointmentData['doctor_id']);
            $stmt->bindParam(':appointment_date', $appointmentData['appointment_date']);
            $stmt->bindParam(':appointment_time', $appointmentData['appointment_time']);
            $stmt->bindParam(':status', $appointmentData['status']);
            $stmt->bindParam(':notes', $appointmentData['notes']);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Delete appointment
     */
    public function deleteAppointment($id) {
        try {
            $appointment = $this->getAppointmentById($id);
            if (!$appointment) {
                throw new Exception('Appointment not found');
            }
            
            // Check if appointment can be deleted (only scheduled appointments)
            if ($appointment['status'] === 'completed') {
                throw new Exception('Cannot delete completed appointments');
            }
            
            $query = "DELETE FROM appointments WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    /**
     * Update appointment status
     */
    public function updateAppointmentStatus($id, $status) {
        try {
            $validStatuses = ['scheduled', 'completed', 'cancelled'];
            if (!in_array($status, $validStatuses)) {
                throw new Exception('Invalid appointment status');
            }
            
            $query = "UPDATE appointments SET status = :status, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':id', $id);
            
            return $stmt->execute();
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    /**
     * Get appointments by patient
     */
    public function getAppointmentsByPatient($patientId) {
        try {
            $query = "SELECT 
                        a.id, a.appointment_date, a.appointment_time, a.status, a.notes,
                        d_user.full_name as doctor_name, d.specialization
                      FROM appointments a
                      LEFT JOIN doctors d ON a.doctor_id = d.id
                      LEFT JOIN user_profiles d_user ON d.user_id = d_user.id
                      WHERE a.patient_id = :patient_id
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':patient_id', $patientId);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * Get appointments by doctor
     */
    public function getAppointmentsByDoctor($doctorId) {
        try {
            $query = "SELECT 
                        a.id, a.appointment_date, a.appointment_time, a.status, a.notes,
                        p_user.full_name as patient_name, p_user.phone as patient_phone
                      FROM appointments a
                      LEFT JOIN patients p ON a.patient_id = p.id
                      LEFT JOIN user_profiles p_user ON p.user_id = p_user.id
                      WHERE a.doctor_id = :doctor_id
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':doctor_id', $doctorId);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * Validate appointment data
     */
    private function validateAppointmentData($appointmentData, $isUpdate = false) {
        $errors = [];

        // Patient ID validation
        if (empty($appointmentData['patient_id'])) {
            $errors[] = 'Patient selection is required';
        } else {
            if (!$this->patientExists($appointmentData['patient_id'])) {
                $errors[] = 'Selected patient does not exist';
            }
        }

        // Doctor ID validation
        if (empty($appointmentData['doctor_id'])) {
            $errors[] = 'Doctor selection is required';
        } else {
            if (!$this->doctorExists($appointmentData['doctor_id'])) {
                $errors[] = 'Selected doctor does not exist';
            }
        }

        // Date validation
        if (empty($appointmentData['appointment_date'])) {
            $errors[] = 'Appointment date is required';
        } else {
            $date = DateTime::createFromFormat('Y-m-d', $appointmentData['appointment_date']);
            if (!$date || $date->format('Y-m-d') !== $appointmentData['appointment_date']) {
                $errors[] = 'Invalid appointment date format';
            } else {
                $today = new DateTime();
                $today->setTime(0, 0, 0);
                if ($date < $today) {
                    $errors[] = 'Appointment date cannot be in the past';
                }

                // Check if date is too far in the future (e.g., 1 year)
                $maxDate = new DateTime('+1 year');
                if ($date > $maxDate) {
                    $errors[] = 'Appointment date cannot be more than 1 year in the future';
                }
            }
        }

        // Time validation
        if (empty($appointmentData['appointment_time'])) {
            $errors[] = 'Appointment time is required';
        } else {
            if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $appointmentData['appointment_time'])) {
                $errors[] = 'Invalid appointment time format';
            } else {
                // Check business hours (8 AM to 6 PM)
                $time = DateTime::createFromFormat('H:i', $appointmentData['appointment_time']);
                $startTime = DateTime::createFromFormat('H:i', '08:00');
                $endTime = DateTime::createFromFormat('H:i', '18:00');

                if ($time < $startTime || $time > $endTime) {
                    $errors[] = 'Appointment time must be between 8:00 AM and 6:00 PM';
                }
            }
        }

        // Status validation
        $validStatuses = ['scheduled', 'completed', 'cancelled'];
        if (empty($appointmentData['status']) || !in_array($appointmentData['status'], $validStatuses)) {
            $appointmentData['status'] = 'scheduled'; // Default status
        }

        // Notes validation (optional but limit length)
        if (!empty($appointmentData['notes']) && strlen($appointmentData['notes']) > 500) {
            $errors[] = 'Notes cannot exceed 500 characters';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }

    /**
     * Check for appointment conflicts
     */
    private function checkAppointmentConflicts($doctorId, $date, $time, $excludeId = null) {
        try {
            $query = "SELECT COUNT(*) as count FROM appointments
                     WHERE doctor_id = :doctor_id
                     AND appointment_date = :date
                     AND appointment_time = :time
                     AND status != 'cancelled'";

            if ($excludeId) {
                $query .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':doctor_id', $doctorId);
            $stmt->bindParam(':date', $date);
            $stmt->bindParam(':time', $time);

            if ($excludeId) {
                $stmt->bindParam(':exclude_id', $excludeId);
            }

            $stmt->execute();
            $count = $stmt->fetch()['count'];

            if ($count > 0) {
                throw new Exception('Doctor already has an appointment at this date and time');
            }
        } catch (PDOException $e) {
            throw new Exception('Error checking appointment conflicts');
        }
    }

    /**
     * Check if patient exists
     */
    private function patientExists($patientId) {
        try {
            $query = "SELECT COUNT(*) as count FROM patients WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $patientId);
            $stmt->execute();

            return $stmt->fetch()['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Check if doctor exists
     */
    private function doctorExists($doctorId) {
        try {
            $query = "SELECT COUNT(*) as count FROM doctors WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $doctorId);
            $stmt->execute();

            return $stmt->fetch()['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStats() {
        try {
            $stats = [];

            // Total appointments
            $query = "SELECT COUNT(*) as count FROM appointments";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['total_appointments'] = $stmt->fetch()['count'];

            // Appointments by status
            $query = "SELECT status, COUNT(*) as count FROM appointments GROUP BY status";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $statusStats = $stmt->fetchAll();

            foreach ($statusStats as $stat) {
                $stats['appointments_' . $stat['status']] = $stat['count'];
            }

            // Today's appointments
            $query = "SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE()";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['today_appointments'] = $stmt->fetch()['count'];

            // This week's appointments
            $query = "SELECT COUNT(*) as count FROM appointments WHERE WEEK(appointment_date) = WEEK(CURDATE()) AND YEAR(appointment_date) = YEAR(CURDATE())";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['week_appointments'] = $stmt->fetch()['count'];

            return $stats;
        } catch (PDOException $e) {
            return [
                'total_appointments' => 0,
                'appointments_scheduled' => 0,
                'appointments_completed' => 0,
                'appointments_cancelled' => 0,
                'today_appointments' => 0,
                'week_appointments' => 0
            ];
        }
    }

    /**
     * Get available time slots for a doctor on a specific date
     */
    public function getAvailableTimeSlots($doctorId, $date) {
        try {
            // Define working hours (8 AM to 6 PM, 30-minute slots)
            $workingHours = [
                '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
                '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
                '16:00', '16:30', '17:00', '17:30'
            ];

            // Get booked appointments for this doctor on this date
            $query = "SELECT appointment_time FROM appointments
                     WHERE doctor_id = :doctor_id
                     AND appointment_date = :date
                     AND status != 'cancelled'";

            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':doctor_id', $doctorId);
            $stmt->bindParam(':date', $date);
            $stmt->execute();

            $bookedTimes = array_column($stmt->fetchAll(), 'appointment_time');

            // Remove booked times from available slots
            $availableSlots = array_diff($workingHours, $bookedTimes);

            return array_values($availableSlots);
        } catch (PDOException $e) {
            return [];
        }
    }
}
?>
